#!/bin/bash

# GameFlex AWS Data Seeding Script - Simplified Version
# This script seeds AWS services with test data for development

echo "[SEED] Starting GameFlex AWS data seeding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Environment variables
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_REGION=${AWS_REGION:-us-west-2}

# Table names
USERS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Users"
USER_PROFILES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles"
POSTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Posts"
MEDIA_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Media"
COMMENTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Comments"
LIKES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Likes"
CHANNELS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Channels"
FOLLOWS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Follows"
MEDIA_BUCKET="${PROJECT_NAME}-media-${ENVIRONMENT}"

# Simple put_item function
put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    print_status "Adding $item_description to $table_name..."
    
    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"
    
    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --item file://"$temp_file" > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
    else
        print_error "❌ Failed to add $item_description"
        # Show the actual error for debugging
        echo "Error details:" >&2
        timeout 30 aws dynamodb put-item \
            --table-name "$table_name" \
            --item file://"$temp_file" 2>&1 | head -3 >&2
    fi
    
    rm -f "$temp_file"
}

# Test AWS connection
print_status "Testing AWS connection..."
if aws sts get-caller-identity > /dev/null 2>&1; then
    print_status "AWS CLI connection successful"
else
    print_error "Failed to connect to AWS"
    exit 1
fi

# Debug: Show table names being used
print_status "Using table names:"
print_status "  Users: $USERS_TABLE"
print_status "  UserProfiles: $USER_PROFILES_TABLE"
print_status "  Posts: $POSTS_TABLE"

# Seed Users
print_header "Seeding Users table..."

put_item "$USERS_TABLE" '{
    "id": {"S": "550e8400-e29b-41d4-a716-************"},
    "cognito_user_id": {"S": "dev-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "developer"},
    "display_name": {"S": "GameFlex Developer"},
    "bio": {"S": "Development account for testing GameFlex features"},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "developer user"

put_item "$USERS_TABLE" '{
    "id": {"S": "550e8400-e29b-41d4-a716-************"},
    "cognito_user_id": {"S": "admin-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "admin"},
    "display_name": {"S": "GameFlex Admin"},
    "bio": {"S": "Administrator account with full access"},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "admin user"

put_item "$USERS_TABLE" '{
    "id": {"S": "********-0000-0000-0000-********0003"},
    "cognito_user_id": {"S": "john-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "johndoe"},
    "display_name": {"S": "John Doe"},
    "bio": {"S": "Gaming enthusiast and content creator."},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "john doe user"

put_item "$USERS_TABLE" '{
    "id": {"S": "********-0000-0000-0000-************"},
    "cognito_user_id": {"S": "jane-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "janesmith"},
    "display_name": {"S": "Jane Smith"},
    "bio": {"S": "Professional gamer and streamer."},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "jane smith user"

put_item "$USERS_TABLE" '{
    "id": {"S": "********-0000-0000-0000-********0005"},
    "cognito_user_id": {"S": "mike-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "mikewilson"},
    "display_name": {"S": "Mike Wilson"},
    "bio": {"S": "Casual gamer who loves sharing gaming moments."},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "mike wilson user"

print_status "Users seeded successfully!"
echo

# Seed UserProfiles
print_header "Seeding UserProfiles table..."

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "first_name": {"S": "Dev"},
    "last_name": {"S": "User"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/New_York"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "dev profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "first_name": {"S": "Admin"},
    "last_name": {"S": "User"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/Los_Angeles"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "admin profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "********-0000-0000-0000-********0003"},
    "first_name": {"S": "John"},
    "last_name": {"S": "Doe"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/New_York"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "john profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "********-0000-0000-0000-************"},
    "first_name": {"S": "Jane"},
    "last_name": {"S": "Smith"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/Los_Angeles"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "jane profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "********-0000-0000-0000-********0005"},
    "first_name": {"S": "Mike"},
    "last_name": {"S": "Wilson"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/Chicago"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "mike profile"

print_status "User profiles seeded successfully!"
echo

# Note: Channels table not defined in SAM template, skipping channels seeding

# Seed Media
print_header "Seeding Media table..."

put_item "$MEDIA_TABLE" '{
    "id": {"S": "30000000-0000-0000-0000-********0001"},
    "type": {"S": "image"},
    "location": {"S": "user"},
    "name": {"S": "cod_screenshot"},
    "extension": {"S": "jpg"},
    "owner_id": {"S": "********-0000-0000-0000-********0003"},
    "bucket_location": {"S": "s3"},
    "bucket_name": {"S": "'$MEDIA_BUCKET'"},
    "bucket_permission": {"S": "public"},
    "s3_url": {"S": "https://'$MEDIA_BUCKET'.s3.'$AWS_REGION'.amazonaws.com/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
    "width": {"N": "1920"},
    "height": {"N": "1080"},
    "size_bytes": {"N": "245760"},
    "mime_type": {"S": "image/jpeg"},
    "created_at": {"S": "2024-12-28T14:30:00Z"},
    "updated_at": {"S": "2024-12-28T14:30:00Z"}
}' "cod screenshot media"

put_item "$MEDIA_TABLE" '{
    "id": {"S": "30000000-0000-0000-0000-********0002"},
    "type": {"S": "image"},
    "location": {"S": "user"},
    "name": {"S": "diablo_screenshot"},
    "extension": {"S": "webp"},
    "owner_id": {"S": "********-0000-0000-0000-************"},
    "bucket_location": {"S": "s3"},
    "bucket_name": {"S": "'$MEDIA_BUCKET'"},
    "bucket_permission": {"S": "public"},
    "s3_url": {"S": "https://'$MEDIA_BUCKET'.s3.'$AWS_REGION'.amazonaws.com/user/********-0000-0000-0000-************/diablo_screenshot.webp"},
    "width": {"N": "1920"},
    "height": {"N": "1080"},
    "size_bytes": {"N": "189440"},
    "mime_type": {"S": "image/webp"},
    "created_at": {"S": "2024-12-27T16:45:00Z"},
    "updated_at": {"S": "2024-12-27T16:45:00Z"}
}' "diablo screenshot media"

print_status "Media seeded successfully!"
echo

# Seed Posts
print_header "Seeding Posts table..."

put_item "$POSTS_TABLE" '{
    "id": {"S": "20000000-0000-0000-0000-********0001"},
    "author_id": {"S": "********-0000-0000-0000-********0003"},
    "cognito_user_id": {"S": "john-cognito-placeholder"},
    "content": {"S": "Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥"},
    "media_id": {"S": "cod_screenshot.jpg"},
    "media_type": {"S": "image"},
    "s3_bucket": {"S": "'$MEDIA_BUCKET'"},
    "s3_key": {"S": "user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
    "like_count": {"N": "12"},
    "comment_count": {"N": "4"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-12-28T14:30:00Z"},
    "updated_at": {"S": "2024-12-28T14:30:00Z"}
}' "john cod post"

put_item "$POSTS_TABLE" '{
    "id": {"S": "20000000-0000-0000-0000-********0002"},
    "author_id": {"S": "********-0000-0000-0000-************"},
    "cognito_user_id": {"S": "jane-cognito-placeholder"},
    "content": {"S": "Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️"},
    "media_id": {"S": "diablo_screenshot.webp"},
    "media_type": {"S": "image"},
    "s3_bucket": {"S": "'$MEDIA_BUCKET'"},
    "s3_key": {"S": "user/********-0000-0000-0000-************/diablo_screenshot.webp"},
    "like_count": {"N": "18"},
    "comment_count": {"N": "6"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-12-27T16:45:00Z"},
    "updated_at": {"S": "2024-12-27T16:45:00Z"}
}' "jane diablo post"

print_status "Posts seeded successfully!"
echo

# Seed Comments
print_header "Seeding Comments table..."

put_item "$COMMENTS_TABLE" '{
    "id": {"S": "40000000-0000-0000-0000-********0001"},
    "post_id": {"S": "20000000-0000-0000-0000-********0001"},
    "user_id": {"S": "********-0000-0000-0000-************"},
    "content": {"S": "Nice clutch! What loadout were you using?"},
    "like_count": {"N": "3"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-12-28T15:15:00Z"},
    "updated_at": {"S": "2024-12-28T15:15:00Z"}
}' "comment 1"

put_item "$COMMENTS_TABLE" '{
    "id": {"S": "40000000-0000-0000-0000-********0002"},
    "post_id": {"S": "20000000-0000-0000-0000-********0001"},
    "user_id": {"S": "********-0000-0000-0000-********0005"},
    "content": {"S": "That was insane! 🔥"},
    "like_count": {"N": "1"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-12-28T16:20:00Z"},
    "updated_at": {"S": "2024-12-28T16:20:00Z"}
}' "comment 2"

put_item "$COMMENTS_TABLE" '{
    "id": {"S": "40000000-0000-0000-0000-********0003"},
    "post_id": {"S": "20000000-0000-0000-0000-********0002"},
    "user_id": {"S": "********-0000-0000-0000-********0003"},
    "content": {"S": "Congrats! What difficulty level?"},
    "like_count": {"N": "2"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-12-27T17:30:00Z"},
    "updated_at": {"S": "2024-12-27T17:30:00Z"}
}' "comment 3"

put_item "$COMMENTS_TABLE" '{
    "id": {"S": "40000000-0000-0000-0000-************"},
    "post_id": {"S": "20000000-0000-0000-0000-********0002"},
    "user_id": {"S": "********-0000-0000-0000-********0005"},
    "content": {"S": "The loot looks amazing! 💎"},
    "like_count": {"N": "4"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-12-27T18:15:00Z"},
    "updated_at": {"S": "2024-12-27T18:15:00Z"}
}' "comment 4"

print_status "Comments seeded successfully!"
echo

# Seed Likes
print_header "Seeding Likes table..."

put_item "$LIKES_TABLE" '{
    "post_id": {"S": "20000000-0000-0000-0000-********0001"},
    "user_id": {"S": "********-0000-0000-0000-************"},
    "created_at": {"S": "2024-12-28T14:35:00Z"}
}' "like 1"

put_item "$LIKES_TABLE" '{
    "post_id": {"S": "20000000-0000-0000-0000-********0001"},
    "user_id": {"S": "********-0000-0000-0000-********0005"},
    "created_at": {"S": "2024-12-28T14:40:00Z"}
}' "like 2"

put_item "$LIKES_TABLE" '{
    "post_id": {"S": "20000000-0000-0000-0000-********0001"},
    "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "created_at": {"S": "2024-12-28T14:45:00Z"}
}' "like 3"

put_item "$LIKES_TABLE" '{
    "post_id": {"S": "20000000-0000-0000-0000-********0002"},
    "user_id": {"S": "********-0000-0000-0000-********0003"},
    "created_at": {"S": "2024-12-27T16:50:00Z"}
}' "like 4"

put_item "$LIKES_TABLE" '{
    "post_id": {"S": "20000000-0000-0000-0000-********0002"},
    "user_id": {"S": "********-0000-0000-0000-********0005"},
    "created_at": {"S": "2024-12-27T17:00:00Z"}
}' "like 5"

put_item "$LIKES_TABLE" '{
    "post_id": {"S": "20000000-0000-0000-0000-********0002"},
    "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "created_at": {"S": "2024-12-27T17:15:00Z"}
}' "like 6"

print_status "Likes seeded successfully!"
echo

# Seed Follows
print_header "Seeding Follows table..."

put_item "$FOLLOWS_TABLE" '{
    "follower_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "following_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "created_at": {"S": "2024-01-01T00:00:00Z"}
}' "dev follows admin"

put_item "$FOLLOWS_TABLE" '{
    "follower_id": {"S": "********-0000-0000-0000-********0003"},
    "following_id": {"S": "********-0000-0000-0000-************"},
    "created_at": {"S": "2024-01-02T00:00:00Z"}
}' "john follows jane"

put_item "$FOLLOWS_TABLE" '{
    "follower_id": {"S": "********-0000-0000-0000-************"},
    "following_id": {"S": "********-0000-0000-0000-********0005"},
    "created_at": {"S": "2024-01-03T00:00:00Z"}
}' "jane follows mike"

put_item "$FOLLOWS_TABLE" '{
    "follower_id": {"S": "********-0000-0000-0000-********0005"},
    "following_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "created_at": {"S": "2024-01-04T00:00:00Z"}
}' "mike follows dev"

# Upload media files to S3
print_header "Uploading media files to S3..."

# Check if media bucket exists
print_status "Checking if S3 bucket exists: $MEDIA_BUCKET"
if aws s3 ls "s3://$MEDIA_BUCKET" > /dev/null 2>&1; then
    print_status "S3 bucket $MEDIA_BUCKET exists and is accessible"
else
    print_error "S3 bucket $MEDIA_BUCKET does not exist!"
    print_error "Make sure your SAM application is deployed: sam build && sam deploy"
    exit 1
fi

# Function to upload file to S3
upload_media_file() {
    local file_path=$1
    local s3_key=$2
    local content_type=$3

    if [ ! -f "$file_path" ]; then
        print_error "Media file not found: $file_path"
        return 1
    fi

    # Check if file already exists in S3
    if aws s3 ls "s3://$MEDIA_BUCKET/$s3_key" > /dev/null 2>&1; then
        print_status "File $s3_key already exists in S3, skipping"
        return 0
    fi

    print_status "Uploading $file_path to s3://$MEDIA_BUCKET/$s3_key"

    if aws s3 cp "$file_path" "s3://$MEDIA_BUCKET/$s3_key" \
        --content-type "$content_type" > /dev/null 2>&1; then
        print_status "✅ Successfully uploaded $s3_key"
    else
        print_error "❌ Failed to upload $s3_key"
        return 1
    fi
}

# Get the script directory to find assets
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ASSETS_DIR="$(dirname "$SCRIPT_DIR")/assets/media"

# Upload media files for seeded posts
print_status "Uploading media files for seeded posts..."

upload_media_file \
    "$ASSETS_DIR/cod_screenshot.jpg" \
    "user/********-0000-0000-0000-********0003/cod_screenshot.jpg" \
    "image/jpeg"

upload_media_file \
    "$ASSETS_DIR/diablo_screenshot.webp" \
    "user/********-0000-0000-0000-************/diablo_screenshot.webp" \
    "image/webp"

print_status "Media upload completed successfully!"
echo

print_status "🎉 Complete seeding finished successfully!"
print_status "Summary:"
print_status "  📊 Seeded 5 users with profiles"
print_status "  📝 Seeded 2 posts with media"
print_status "  💬 Seeded 4 comments"
print_status "  ❤️  Seeded 6 likes"
print_status "  👥 Seeded 4 follow relationships"
print_status "  📸 Seeded 2 media items"
print_status ""
print_status "Your GameFlex backend is now ready for testing!"
